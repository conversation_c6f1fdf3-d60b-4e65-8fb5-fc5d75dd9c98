#!/bin/bash

# 构建性能测试脚本
# 比较单线程和多线程编译性能

set -e

echo "=== 构建性能测试 ==="
echo ""

# 获取系统信息
CPU_CORES=$(nproc 2>/dev/null || echo "4")
echo "系统信息:"
echo "  CPU核心数: $CPU_CORES"
echo "  推荐并行任务数: $CPU_CORES"
echo ""

# 确保构建目录存在
if [ ! -d "build" ]; then
    mkdir build
fi

cd build

# 清理构建
echo "清理构建环境..."
make clean 2>/dev/null || true
rm -f bin/FaultDetectRefactored 2>/dev/null || true

# 运行CMake配置
echo "配置项目..."
cmake -DCMAKE_BUILD_TYPE=Release .. > /dev/null 2>&1

echo ""
echo "=== 开始性能测试 ==="

# 测试1：单线程编译
echo ""
echo "测试1: 单线程编译"
make clean > /dev/null 2>&1
START_TIME=$(date +%s)
make -j1 > /dev/null 2>&1
END_TIME=$(date +%s)
SINGLE_THREAD_TIME=$((END_TIME - START_TIME))
echo "  单线程编译时间: ${SINGLE_THREAD_TIME}秒"

# 测试2：多线程编译（CPU核心数）
echo ""
echo "测试2: 多线程编译 (${CPU_CORES}个并行任务)"
make clean > /dev/null 2>&1
START_TIME=$(date +%s)
make -j$CPU_CORES > /dev/null 2>&1
END_TIME=$(date +%s)
MULTI_THREAD_TIME=$((END_TIME - START_TIME))
echo "  多线程编译时间: ${MULTI_THREAD_TIME}秒"

# 测试3：最大并行编译
MAX_JOBS=$((CPU_CORES * 2))
echo ""
echo "测试3: 最大并行编译 (${MAX_JOBS}个并行任务)"
make clean > /dev/null 2>&1
START_TIME=$(date +%s)
make -j$MAX_JOBS > /dev/null 2>&1
END_TIME=$(date +%s)
MAX_PARALLEL_TIME=$((END_TIME - START_TIME))
echo "  最大并行编译时间: ${MAX_PARALLEL_TIME}秒"

# 计算性能提升
echo ""
echo "=== 性能对比结果 ==="
if [ $SINGLE_THREAD_TIME -gt 0 ]; then
    SPEEDUP_MULTI=$(echo "scale=2; $SINGLE_THREAD_TIME / $MULTI_THREAD_TIME" | bc -l 2>/dev/null || echo "N/A")
    SPEEDUP_MAX=$(echo "scale=2; $SINGLE_THREAD_TIME / $MAX_PARALLEL_TIME" | bc -l 2>/dev/null || echo "N/A")
    
    echo "单线程编译:     ${SINGLE_THREAD_TIME}秒"
    echo "多线程编译:     ${MULTI_THREAD_TIME}秒 (${SPEEDUP_MULTI}x 加速)"
    echo "最大并行编译:   ${MAX_PARALLEL_TIME}秒 (${SPEEDUP_MAX}x 加速)"
    
    # 推荐最佳配置
    if [ $MULTI_THREAD_TIME -le $MAX_PARALLEL_TIME ]; then
        echo ""
        echo "推荐配置: make -j$CPU_CORES (最佳性能)"
    else
        echo ""
        echo "推荐配置: make -j$MAX_JOBS (最佳性能)"
    fi
else
    echo "性能测试失败"
fi

echo ""
echo "=== 测试完成 ==="

# 验证最终构建结果
if [ -f "bin/FaultDetectRefactored" ]; then
    echo "✓ 构建成功，可执行文件已生成"
    ls -lh bin/FaultDetectRefactored
else
    echo "✗ 构建失败"
    exit 1
fi
