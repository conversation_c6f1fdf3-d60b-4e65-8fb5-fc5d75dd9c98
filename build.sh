#!/bin/bash

# 拉吊索缺损识别系统构建脚本
# 版本: 1.0.0
# 日期: 2025-07-30

set -e  # 遇到错误时退出

echo "=== 拉吊索缺损识别系统构建脚本 ==="
echo "版本: 1.0.0"
echo "构建目标: 重构版本 (main_refactored.cpp)"

# 获取系统信息
CPU_CORES=$(nproc 2>/dev/null || echo "4")
TOTAL_MEM=$(free -m 2>/dev/null | awk '/^Mem:/{print $2}' || echo "4096")
BUILD_TYPE=${BUILD_TYPE:-Release}

echo "系统信息:"
echo "  CPU核心数: $CPU_CORES"
echo "  总内存: ${TOTAL_MEM}MB"
echo "  构建类型: $BUILD_TYPE"
echo ""

# 检查构建目录
if [ ! -d "build" ]; then
    echo "创建构建目录..."
    mkdir build
fi

cd build

# 清理之前的构建
echo "清理之前的构建文件..."
make clean 2>/dev/null || true
rm -f bin/FaultDetect 2>/dev/null || true  # 删除旧版本可执行文件

# 运行CMake配置
echo "运行CMake配置..."
cmake -DCMAKE_BUILD_TYPE=$BUILD_TYPE ..

# 编译项目
echo "编译项目（使用 $CPU_CORES 个并行任务）..."
START_TIME=$(date +%s)
make -j$CPU_CORES
END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))
echo "编译耗时: ${BUILD_TIME}秒"

# 检查编译结果
if [ -f "bin/FaultDetectRefactored" ]; then
    echo ""
    echo "=== 构建成功 ==="
    echo "可执行文件: build/bin/FaultDetectRefactored"
    echo ""
    echo "使用方法:"
    echo "  cd build"
    echo "  ./bin/FaultDetectRefactored          # 运行生产模式"
    echo "  ./bin/FaultDetectRefactored --help   # 查看帮助"
    echo "  ./bin/FaultDetectRefactored --test basic  # 运行基础测试"
    echo ""
    
    # 显示文件信息
    ls -lh bin/FaultDetectRefactored
    
    echo ""
    echo "=== 构建完成 ==="
else
    echo ""
    echo "=== 构建失败 ==="
    echo "未找到可执行文件 bin/FaultDetectRefactored"
    exit 1
fi
