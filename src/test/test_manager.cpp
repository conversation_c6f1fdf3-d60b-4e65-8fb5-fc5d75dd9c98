#include "../../include/test/test_manager.h"
#include <iostream>
#include <iomanip>

TestManager::TestManager() {
    resetTestResults();
}

TestManager::~TestManager() = default;

bool TestManager::runAllTests() {
    Utils::logInfo("=== 开始运行所有测试 ===");
    resetTestResults();
    
    bool allPassed = true;
    
    // 运行基础功能测试
    Utils::logInfo("--- 基础功能测试 ---");
    bool basicPassed = runBasicTests();
    allPassed &= basicPassed;
    
#ifdef USE_OPENCV
    // 运行摄像头功能测试
    Utils::logInfo("--- 摄像头功能测试 ---");
    bool cameraPassed = runCameraTests();
    allPassed &= cameraPassed;
    
    // 运行系统集成测试
    Utils::logInfo("--- 系统集成测试 ---");
    bool systemPassed = runSystemTests();
    allPassed &= systemPassed;
#endif
    
    displayTestSummary();
    
    if (allPassed) {
        Utils::logInfo("=== 所有测试通过 ===");
    } else {
        Utils::logError("=== 部分测试失败 ===");
    }
    
    return allPassed;
}

bool TestManager::runBasicTests() {
    Utils::logInfo("开始基础功能测试...");
    
    bool allPassed = BasicFunctionTest::runAllTests();
    recordTestResult("基础功能测试", allPassed);
    
    return allPassed;
}

bool TestManager::runCameraTests() {
#ifdef USE_OPENCV
    Utils::logInfo("开始摄像头功能测试...");
    
    bool allPassed = CameraFunctionTest::runAllTests();
    recordTestResult("摄像头功能测试", allPassed);
    
    return allPassed;
#else
    Utils::logWarning("OpenCV未启用，跳过摄像头功能测试");
    return true;
#endif
}

bool TestManager::runSystemTests() {
#ifdef USE_OPENCV
    Utils::logInfo("开始系统集成测试...");
    
    bool allPassed = SystemIntegrationTest::runAllTests();
    recordTestResult("系统集成测试", allPassed);
    
    return allPassed;
#else
    Utils::logWarning("OpenCV未启用，跳过系统集成测试");
    return true;
#endif
}

void TestManager::recordTestResult(const std::string& testName, bool passed) {
    testResults.totalTests++;
    if (passed) {
        testResults.passedTests++;
        Utils::logInfo("✓ " + testName + ": 通过");
    } else {
        testResults.failedTests++;
        testResults.failedTestNames.push_back(testName);
        Utils::logError("✗ " + testName + ": 失败");
    }
}

void TestManager::displayTestSummary() const {
    Utils::logInfo("=== 测试结果摘要 ===");
    Utils::logInfo("总测试数: " + std::to_string(testResults.totalTests));
    Utils::logInfo("通过测试: " + std::to_string(testResults.passedTests));
    Utils::logInfo("失败测试: " + std::to_string(testResults.failedTests));
    Utils::logInfo("成功率: " + std::to_string(testResults.getSuccessRate()) + "%");
    
    if (!testResults.failedTestNames.empty()) {
        Utils::logError("失败的测试:");
        for (const auto& testName : testResults.failedTestNames) {
            Utils::logError("  - " + testName);
        }
    }
}

void TestManager::resetTestResults() {
    testResults.totalTests = 0;
    testResults.passedTests = 0;
    testResults.failedTests = 0;
    testResults.failedTestNames.clear();
}

// BasicFunctionTest 实现
bool BasicFunctionTest::runAllTests() {
    Utils::logInfo("运行基础功能测试...");
    
    bool allPassed = true;
    
    // 测试时间函数
    allPassed &= testTimeFunctions();
    
    // 测试损伤类型转换
    allPassed &= testDamageTypeConversion();
    
    // 测试单位转换
    allPassed &= testUnitConversion();
    
    // 测试目录创建
    allPassed &= testDirectoryCreation();
    
    // 测试工具函数
    allPassed &= testUtilityFunctions();
    
    if (allPassed) {
        Utils::logInfo("基础功能测试: 全部通过");
    } else {
        Utils::logError("基础功能测试: 部分失败");
    }
    
    return allPassed;
}

bool BasicFunctionTest::testTimeFunctions() {
    Utils::logInfo("测试时间函数...");
    
    try {
        std::string timeStr = Utils::getCurrentTimeString();
        if (timeStr.empty()) {
            Utils::logError("时间函数返回空字符串");
            return false;
        }
        
        Utils::logInfo("当前时间: " + timeStr);
        Utils::logInfo("✓ 时间函数测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("时间函数测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testDamageTypeConversion() {
    Utils::logInfo("测试损伤类型转换...");
    
    try {
        bool allPassed = true;
        
        // 测试所有损伤类型
        for (int i = 0; i <= 7; ++i) {
            DamageType type = static_cast<DamageType>(i);
            std::string typeStr = Utils::damageTypeToString(type);
            
            if (typeStr.empty()) {
                Utils::logError("损伤类型 " + std::to_string(i) + " 转换失败");
                allPassed = false;
            } else {
                Utils::logInfo("损伤类型 " + std::to_string(i) + ": " + typeStr);
            }
        }
        
        if (allPassed) {
            Utils::logInfo("✓ 损伤类型转换测试通过");
        } else {
            Utils::logError("✗ 损伤类型转换测试失败");
        }
        
        return allPassed;
    } catch (const std::exception& e) {
        Utils::logError("损伤类型转换测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testUnitConversion() {
    Utils::logInfo("测试单位转换...");
    
    try {
        double pixels = 100.0;
        double mm = Utils::pixelToMm(pixels);
        double backToPixels = Utils::mmToPixel(mm);
        
        // 检查转换精度
        double error = std::abs(pixels - backToPixels);
        if (error > 0.001) {
            Utils::logError("单位转换精度不足，误差: " + std::to_string(error));
            return false;
        }
        
        Utils::logInfo("像素转换测试: " + std::to_string(pixels) + " pixels = " +
                       std::to_string(mm) + " mm = " + std::to_string(backToPixels) + " pixels");
        Utils::logInfo("✓ 单位转换测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("单位转换测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testDirectoryCreation() {
    Utils::logInfo("测试目录创建...");
    
    try {
        std::string testDir = "output/test_basic";
        bool success = Utils::createDirectory(testDir);
        
        if (success) {
            Utils::logInfo("✓ 测试目录创建成功: " + testDir);
            return true;
        } else {
            Utils::logError("✗ 测试目录创建失败: " + testDir);
            return false;
        }
    } catch (const std::exception& e) {
        Utils::logError("目录创建测试异常: " + std::string(e.what()));
        return false;
    }
}

bool BasicFunctionTest::testUtilityFunctions() {
    Utils::logInfo("测试工具函数...");

    try {
        // 这里可以添加更多工具函数的测试
        // 目前主要测试日志功能是否正常
        Utils::logInfo("工具函数测试信息");
        Utils::logWarning("工具函数测试警告");

        Utils::logInfo("✓ 工具函数测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("工具函数测试异常: " + std::string(e.what()));
        return false;
    }
}

#ifdef USE_OPENCV
// CameraFunctionTest 实现
bool CameraFunctionTest::runAllTests() {
    Utils::logInfo("运行摄像头功能测试...");

    bool allPassed = true;

    // 测试摄像头检测
    allPassed &= testCameraDetection();

    // 如果检测成功，继续其他测试
    if (allPassed) {
        // 创建摄像头管理器进行测试
        CameraManager manager;
        if (manager.initialize() && manager.startCapture()) {
            // 测试单摄像头采集
            allPassed &= testSingleCameraCapture(manager);

            // 测试多摄像头采集（智能选择测试类型）
            if (allPassed) {
                allPassed &= testMultiCameraCapture(manager);
            }

            // 测试所有摄像头采集功能（无论单摄像头还是多摄像头）
            if (allPassed) {
                allPassed &= testAllCamerasFunction(manager);
            }

            // 测试图像质量
            if (allPassed) {
                allPassed &= testImageQuality(manager);
            }

            manager.stopCapture();
        } else {
            Utils::logError("摄像头管理器初始化失败");
            allPassed = false;
        }
    }

    if (allPassed) {
        Utils::logInfo("摄像头功能测试: 全部通过");
    } else {
        Utils::logError("摄像头功能测试: 部分失败");
    }

    return allPassed;
}

bool CameraFunctionTest::testCameraDetection() {
    Utils::logInfo("测试摄像头检测...");

    try {
        CameraManager manager;
        bool success = manager.initialize();

        if (success) {
            auto cameraInfos = manager.getAllCameraInfo();
            Utils::logInfo("检测到 " + std::to_string(cameraInfos.size()) + " 个摄像头");

            for (const auto& info : cameraInfos) {
                Utils::logInfo("摄像头 " + std::to_string(info.id) + ": " +
                              (info.status != CameraStatus::DISCONNECTED ? "可用" : "不可用"));
            }

            Utils::logInfo("✓ 摄像头检测测试通过");
            return true;
        } else {
            Utils::logError("✗ 摄像头检测失败");
            return false;
        }
    } catch (const std::exception& e) {
        Utils::logError("摄像头检测测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testSingleCameraCapture(CameraManager& manager) {
    Utils::logInfo("测试单摄像头采集...");

    try {
        // 查找第一个可用摄像头
        int availableCameraId = -1;
        auto cameraInfos = manager.getAllCameraInfo();
        for (size_t i = 0; i < cameraInfos.size(); ++i) {
            if (manager.isCameraAvailable(static_cast<int>(i))) {
                availableCameraId = static_cast<int>(i);
                break;
            }
        }

        if (availableCameraId == -1) {
            Utils::logError("没有可用的摄像头进行测试");
            return false;
        }

        Utils::logInfo("使用摄像头 " + std::to_string(availableCameraId) + " 进行测试");

        // 创建输出目录
        std::string outputDir = "output/test_camera_single";
        Utils::createDirectory(outputDir);

        cv::Mat frame;
        int frameCount = 0;
        int maxFrames = 20; // 减少测试帧数
        int failureCount = 0;
        int maxFailures = 10;

        auto startTime = std::chrono::steady_clock::now();
        auto maxTestTime = std::chrono::seconds(15); // 最大测试时间15秒

        while (frameCount < maxFrames && failureCount < maxFailures) {
            // 检查是否超时
            auto currentTime = std::chrono::steady_clock::now();
            if (currentTime - startTime > maxTestTime) {
                Utils::logWarning("单摄像头测试超时");
                break;
            }

            if (manager.getFrame(availableCameraId, frame)) {
                frameCount++;
                failureCount = 0;

                // 保存第一帧作为样本
                if (frameCount == 1) {
                    std::string filename = outputDir + "/sample_frame.jpg";
                    if (Utils::saveImage(frame, filename)) {
                        Utils::logInfo("保存样本图像: " + filename);
                    }
                }
            } else {
                failureCount++;
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        }

        auto endTime = std::chrono::steady_clock::now();
        auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            endTime - startTime).count();

        Utils::logInfo("单摄像头测试结果:");
        Utils::logInfo("  采集帧数: " + std::to_string(frameCount) + "/" + std::to_string(maxFrames));
        Utils::logInfo("  失败次数: " + std::to_string(failureCount));
        Utils::logInfo("  测试时间: " + std::to_string(totalTime) + " ms");

        bool success = frameCount > 0;
        if (success) {
            Utils::logInfo("✓ 单摄像头采集测试通过");
        } else {
            Utils::logError("✗ 单摄像头采集测试失败");
        }

        return success;
    } catch (const std::exception& e) {
        Utils::logError("单摄像头采集测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testMultiCameraCapture(CameraManager& manager) {
    Utils::logInfo("测试多摄像头采集...");

    try {
        // 首先检查配置的摄像头数量
        int configuredCameras = Config::CAMERA_COUNT;
        Utils::logInfo("配置的摄像头数量: " + std::to_string(configuredCameras));

        // 检查实际可用的摄像头数量
        auto cameraInfos = manager.getAllCameraInfo();
        int availableCameras = 0;
        for (const auto& info : cameraInfos) {
            if (manager.isCameraAvailable(info.id)) {
                availableCameras++;
            }
        }
        Utils::logInfo("实际可用摄像头数量: " + std::to_string(availableCameras));

        // 如果只有一个摄像头，则跳过多摄像头测试
        if (configuredCameras <= 1 || availableCameras <= 1) {
            Utils::logWarning("检测到单摄像头环境，跳过多摄像头采集测试");
            Utils::logInfo("建议: 连接多个摄像头或修改配置文件以启用多摄像头测试");
            Utils::logInfo("✓ 多摄像头测试: 跳过（单摄像头环境）");
            return true; // 在单摄像头环境下跳过测试算作通过
        }

        std::vector<cv::Mat> frames;
        int testFrames = 10;
        int successCount = 0;
        int failureCount = 0;
        int maxFailures = 5;
        int validFrameCount = 0; // 记录有效帧数量

        auto startTime = std::chrono::steady_clock::now();
        auto maxTestTime = std::chrono::seconds(10);

        Utils::createDirectory("output/test_camera_multi");

        for (int i = 0; i < testFrames && failureCount < maxFailures; ++i) {
            auto currentTime = std::chrono::steady_clock::now();
            if (currentTime - startTime > maxTestTime) {
                Utils::logWarning("多摄像头测试超时");
                break;
            }

            if (manager.getAllFrames(frames)) {
                // 检查实际获取到的有效帧数
                int currentValidFrames = 0;
                for (size_t j = 0; j < frames.size(); ++j) {
                    if (!frames[j].empty()) {
                        currentValidFrames++;
                    }
                }

                // 只有当获取到多个有效帧时才算成功
                if (currentValidFrames >= 2) {
                    successCount++;
                    validFrameCount = currentValidFrames;
                    failureCount = 0;

                    // 保存第一组图像作为样本
                    if (i == 0) {
                        int savedCount = 0;
                        for (size_t j = 0; j < frames.size(); ++j) {
                            if (!frames[j].empty()) {
                                std::string filename = "output/test_camera_multi/camera_" +
                                                     std::to_string(j) + "_sample.jpg";
                                if (Utils::saveImage(frames[j], filename)) {
                                    savedCount++;
                                }
                            }
                        }
                        Utils::logInfo("成功保存了 " + std::to_string(savedCount) + " 个摄像头的样本图像");
                    }
                } else {
                    Utils::logWarning("获取到的有效帧数不足: " + std::to_string(currentValidFrames) +
                                     " (需要至少2个)");
                    failureCount++;
                }
            } else {
                failureCount++;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        double successRate = testFrames > 0 ? (double)successCount / testFrames * 100.0 : 0.0;

        Utils::logInfo("多摄像头测试结果:");
        Utils::logInfo("  配置摄像头数: " + std::to_string(configuredCameras));
        Utils::logInfo("  可用摄像头数: " + std::to_string(availableCameras));
        Utils::logInfo("  测试帧数: " + std::to_string(testFrames));
        Utils::logInfo("  成功次数: " + std::to_string(successCount));
        Utils::logInfo("  有效帧数: " + std::to_string(validFrameCount));
        Utils::logInfo("  成功率: " + std::to_string(successRate) + "%");

        // 提高成功率要求到80%，确保多摄像头真正工作
        bool success = successRate >= 80.0 && validFrameCount >= 2;
        if (success) {
            Utils::logInfo("✓ 多摄像头采集测试通过");
        } else {
            Utils::logError("✗ 多摄像头采集测试失败");
            if (successRate < 80.0) {
                Utils::logError("  失败原因: 成功率过低 (" + std::to_string(successRate) + "% < 80%)");
            }
            if (validFrameCount < 2) {
                Utils::logError("  失败原因: 有效摄像头数量不足 (" + std::to_string(validFrameCount) + " < 2)");
            }
        }

        return success;
    } catch (const std::exception& e) {
        Utils::logError("多摄像头采集测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testImageQuality(CameraManager& manager) {
    Utils::logInfo("测试图像质量...");

    try {
        cv::Mat frame;
        auto cameraInfos = manager.getAllCameraInfo();
        bool allPassed = true;

        for (const auto& info : cameraInfos) {
            if (manager.isCameraAvailable(info.id)) {
                if (manager.getFrame(info.id, frame)) {
                    bool qualityOk = validateImageQuality(frame);
                    Utils::logInfo("摄像头 " + std::to_string(info.id) + " 图像质量: " +
                                  (qualityOk ? "合格" : "不合格"));
                    allPassed &= qualityOk;
                } else {
                    Utils::logWarning("无法获取摄像头 " + std::to_string(info.id) + " 的图像");
                    allPassed = false;
                }
            }
        }

        if (allPassed) {
            Utils::logInfo("✓ 图像质量测试通过");
        } else {
            Utils::logError("✗ 图像质量测试失败");
        }

        return allPassed;
    } catch (const std::exception& e) {
        Utils::logError("图像质量测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::testAllCamerasFunction(CameraManager& manager) {
    Utils::logInfo("测试所有摄像头采集功能...");

    try {
        int configuredCameras = Config::CAMERA_COUNT;
        Utils::logInfo("测试 getAllFrames() 方法在当前环境下的行为");

        std::vector<cv::Mat> frames;
        int testFrames = 5;
        int successCount = 0;
        int totalValidFrames = 0;

        for (int i = 0; i < testFrames; ++i) {
            if (manager.getAllFrames(frames)) {
                successCount++;

                // 统计有效帧数
                int validFrames = 0;
                for (size_t j = 0; j < frames.size(); ++j) {
                    if (!frames[j].empty()) {
                        validFrames++;
                    }
                }
                totalValidFrames += validFrames;

                Utils::logInfo("第 " + std::to_string(i+1) + " 次采集: 获取到 " +
                              std::to_string(validFrames) + "/" + std::to_string(frames.size()) + " 个有效帧");
            } else {
                Utils::logWarning("第 " + std::to_string(i+1) + " 次采集失败");
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }

        double avgValidFrames = testFrames > 0 ? (double)totalValidFrames / testFrames : 0.0;
        double successRate = testFrames > 0 ? (double)successCount / testFrames * 100.0 : 0.0;

        Utils::logInfo("所有摄像头采集功能测试结果:");
        Utils::logInfo("  配置摄像头数: " + std::to_string(configuredCameras));
        Utils::logInfo("  测试次数: " + std::to_string(testFrames));
        Utils::logInfo("  成功次数: " + std::to_string(successCount));
        Utils::logInfo("  成功率: " + std::to_string(successRate) + "%");
        Utils::logInfo("  平均有效帧数: " + std::to_string(avgValidFrames));

        // 根据配置的摄像头数量调整期望
        bool success = false;
        if (configuredCameras == 1) {
            // 单摄像头环境：期望获取1个有效帧
            success = (successRate >= 80.0) && (avgValidFrames >= 0.8);
            Utils::logInfo("单摄像头环境评估: " + std::string(success ? "符合预期" : "不符合预期"));
        } else {
            // 多摄像头环境：期望获取多个有效帧
            success = (successRate >= 80.0) && (avgValidFrames >= configuredCameras * 0.8);
            Utils::logInfo("多摄像头环境评估: " + std::string(success ? "符合预期" : "不符合预期"));
        }

        if (success) {
            Utils::logInfo("✓ 所有摄像头采集功能测试通过");
        } else {
            Utils::logError("✗ 所有摄像头采集功能测试失败");
        }

        return success;
    } catch (const std::exception& e) {
        Utils::logError("所有摄像头采集功能测试异常: " + std::string(e.what()));
        return false;
    }
}

bool CameraFunctionTest::validateImageQuality(const cv::Mat& frame) {
    if (frame.empty()) {
        return false;
    }

    // 检查图像尺寸
    if (frame.cols < 320 || frame.rows < 240) {
        Utils::logWarning("图像尺寸过小: " + std::to_string(frame.cols) + "x" + std::to_string(frame.rows));
        return false;
    }

    // 检查图像是否过暗或过亮
    cv::Scalar meanValue = cv::mean(frame);
    double brightness = meanValue[0]; // 假设是灰度图或取第一个通道

    if (brightness < 30 || brightness > 225) {
        Utils::logWarning("图像亮度异常: " + std::to_string(brightness));
        return false;
    }

    return true;
}

// SystemIntegrationTest 实现
bool SystemIntegrationTest::runAllTests() {
    Utils::logInfo("运行系统集成测试...");

    bool allPassed = true;

    // 测试系统启动和关闭
    allPassed &= testSystemStartupShutdown();

    // 其他集成测试可以在这里添加
    // allPassed &= testEndToEndFunctionality();
    // allPassed &= testPerformanceStress();
    // allPassed &= testErrorRecovery();

    if (allPassed) {
        Utils::logInfo("系统集成测试: 全部通过");
    } else {
        Utils::logError("系统集成测试: 部分失败");
    }

    return allPassed;
}

bool SystemIntegrationTest::testSystemStartupShutdown() {
    Utils::logInfo("测试系统启动和关闭...");

    try {
        // 这里可以添加系统启动和关闭的测试逻辑
        // 目前简化为基本的功能验证
        Utils::logInfo("✓ 系统启动关闭测试通过");
        return true;
    } catch (const std::exception& e) {
        Utils::logError("系统启动关闭测试异常: " + std::string(e.what()));
        return false;
    }
}

bool SystemIntegrationTest::testEndToEndFunctionality() {
    // 端到端功能测试的实现
    return true;
}

bool SystemIntegrationTest::testPerformanceStress() {
    // 性能压力测试的实现
    return true;
}

bool SystemIntegrationTest::testErrorRecovery() {
    // 错误恢复测试的实现
    return true;
}

#endif // USE_OPENCV
