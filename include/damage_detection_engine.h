#pragma once

#include "common.h"
#ifdef USE_OPENCV
#include <opencv2/opencv.hpp>
#include <vector>
#include <memory>
#include <chrono>

/**
 * @brief 缺损检测结果结构
 */
struct DamageResult {
    DamageType type;                    // 缺损类型
    cv::Rect boundingBox;              // 缺损区域边界框
    double confidence;                  // 置信度 (0.0-1.0)
    double size_mm;                    // 缺损尺寸（毫米）
    cv::Point2f center;                // 缺损中心点
    std::string description;           // 缺损描述
    std::chrono::system_clock::time_point timestamp; // 检测时间
    
    DamageResult() : type(DamageType::NONE), confidence(0.0), size_mm(0.0) {
        timestamp = std::chrono::system_clock::now();
    }
};

/**
 * @brief 图像质量评估结果
 */
struct ImageQuality {
    double sharpness;      // 清晰度 (0.0-1.0)
    double brightness;     // 亮度 (0.0-1.0)
    double contrast;       // 对比度 (0.0-1.0)
    bool isGoodQuality;    // 是否满足检测要求
    
    ImageQuality() : sharpness(0.0), brightness(0.0), contrast(0.0), isGoodQuality(false) {}
};

/**
 * @brief 缺损检测引擎类
 * 
 * 负责拉吊索缺损的检测和分析，包括：
 * - 图像预处理和质量评估
 * - 7种类型缺损检测算法
 * - 检测结果分析和输出
 * - 性能监控和优化
 * 
 * 支持的缺损类型：
 * - 裂缝 (CRACK): ≥0.1mm
 * - 磨损 (WEAR): 表面磨损
 * - 刮伤 (SCRATCH): 线性划痕
 * - 凹坑 (DENT): 局部凹陷
 * - 鼓包 (BULGE): 局部凸起
 * - 老化 (AGING): 材料老化
 * - 安装破损 (INSTALLATION_DAMAGE): 安装相关损伤
 */
class DamageDetectionEngine {
public:
    DamageDetectionEngine();
    ~DamageDetectionEngine();
    
    /**
     * @brief 初始化检测引擎
     * @return 初始化是否成功
     */
    bool initialize();
    
    /**
     * @brief 停止检测引擎
     */
    void stop();
    
    /**
     * @brief 检测图像中的缺损
     * @param image 输入图像
     * @param cameraId 摄像头ID
     * @return 检测到的缺损列表
     */
    std::vector<DamageResult> detectDamage(const cv::Mat& image, int cameraId);
    
    /**
     * @brief 保存检测结果
     * @param results 检测结果
     * @param cameraId 摄像头ID
     * @return 保存是否成功
     */
    bool saveResults(const std::vector<DamageResult>& results, int cameraId);
    
    /**
     * @brief 评估图像质量
     * @param image 输入图像
     * @return 图像质量评估结果
     */
    ImageQuality assessImageQuality(const cv::Mat& image);
    
    /**
     * @brief 获取检测统计信息
     */
    struct DetectionStats {
        int totalFramesProcessed = 0;
        int totalDamagesDetected = 0;
        std::map<DamageType, int> damageTypeCounts;
        double averageProcessingTime_ms = 0.0;
        double averageConfidence = 0.0;
        
        void reset() {
            totalFramesProcessed = 0;
            totalDamagesDetected = 0;
            damageTypeCounts.clear();
            averageProcessingTime_ms = 0.0;
            averageConfidence = 0.0;
        }
    };
    
    DetectionStats getDetectionStats() const { return stats; }
    void resetStats() { stats.reset(); }
    
private:
    DetectionStats stats;
    bool initialized;
    
    // 算法参数
    struct AlgorithmParams {
        // 裂缝检测参数
        double crackMinLength = 5.0;      // 最小裂缝长度（像素）
        double crackMaxWidth = 3.0;       // 最大裂缝宽度（像素）
        double crackThreshold = 0.1;      // 裂缝检测阈值
        
        // 磨损检测参数
        double wearAreaThreshold = 100.0;  // 磨损区域阈值（像素²）
        double wearIntensityThreshold = 0.3; // 磨损强度阈值
        
        // 刮伤检测参数
        double scratchMinLength = 10.0;    // 最小刮伤长度（像素）
        double scratchMaxWidth = 2.0;      // 最大刮伤宽度（像素）
        
        // 凹坑检测参数
        double dentMinArea = 50.0;         // 最小凹坑面积（像素²）
        double dentMaxArea = 1000.0;       // 最大凹坑面积（像素²）
        
        // 鼓包检测参数
        double bulgeMinArea = 50.0;        // 最小鼓包面积（像素²）
        double bulgeMaxArea = 1000.0;      // 最大鼓包面积（像素²）
        
        // 老化检测参数
        double agingColorThreshold = 0.2;  // 老化颜色变化阈值
        double agingTextureThreshold = 0.3; // 老化纹理变化阈值
        
        // 安装破损检测参数
        double installDamageMinArea = 100.0; // 最小安装破损面积（像素²）
    } params;
    
    /**
     * @brief 图像预处理
     * @param image 输入图像
     * @return 预处理后的图像
     */
    cv::Mat preprocessImage(const cv::Mat& image);
    
    /**
     * @brief 检测裂缝
     * @param image 预处理后的图像
     * @return 检测到的裂缝
     */
    std::vector<DamageResult> detectCracks(const cv::Mat& image);
    
    /**
     * @brief 检测磨损
     * @param image 预处理后的图像
     * @return 检测到的磨损
     */
    std::vector<DamageResult> detectWear(const cv::Mat& image);
    
    /**
     * @brief 检测刮伤
     * @param image 预处理后的图像
     * @return 检测到的刮伤
     */
    std::vector<DamageResult> detectScratches(const cv::Mat& image);
    
    /**
     * @brief 检测凹坑
     * @param image 预处理后的图像
     * @return 检测到的凹坑
     */
    std::vector<DamageResult> detectDents(const cv::Mat& image);
    
    /**
     * @brief 检测鼓包
     * @param image 预处理后的图像
     * @return 检测到的鼓包
     */
    std::vector<DamageResult> detectBulges(const cv::Mat& image);
    
    /**
     * @brief 检测老化
     * @param image 预处理后的图像
     * @return 检测到的老化
     */
    std::vector<DamageResult> detectAging(const cv::Mat& image);
    
    /**
     * @brief 检测安装破损
     * @param image 预处理后的图像
     * @return 检测到的安装破损
     */
    std::vector<DamageResult> detectInstallationDamage(const cv::Mat& image);
    
    /**
     * @brief 后处理检测结果
     * @param results 原始检测结果
     * @return 后处理后的结果
     */
    std::vector<DamageResult> postprocessResults(const std::vector<DamageResult>& results);
    
    /**
     * @brief 更新统计信息
     * @param results 检测结果
     * @param processingTime 处理时间（毫秒）
     */
    void updateStats(const std::vector<DamageResult>& results, double processingTime);
    
    /**
     * @brief 计算缺损尺寸（像素转毫米）
     * @param pixelSize 像素尺寸
     * @return 毫米尺寸
     */
    double calculateSizeInMm(double pixelSize);
    
    /**
     * @brief 生成缺损描述
     * @param result 缺损结果
     * @return 描述字符串
     */
    std::string generateDescription(const DamageResult& result);
};

#endif // USE_OPENCV
