#pragma once

#include "../common.h"
#ifdef USE_OPENCV
#include "../camera_manager.h"
#endif

/**
 * @brief 测试管理器类
 * 
 * 负责协调和管理所有测试功能，包括：
 * - 基础功能测试
 * - 摄像头功能测试
 * - 系统集成测试
 * 
 * 设计原则：
 * - 高内聚：每个测试模块专注于特定功能
 * - 低耦合：测试模块间相互独立
 * - 可扩展：易于添加新的测试类型
 */
class TestManager {
public:
    TestManager();
    ~TestManager();
    
    /**
     * @brief 运行所有测试
     * @return 测试是否全部通过
     */
    bool runAllTests();
    
    /**
     * @brief 运行基础功能测试
     * @return 测试是否通过
     */
    bool runBasicTests();
    
    /**
     * @brief 运行摄像头功能测试
     * @return 测试是否通过
     */
    bool runCameraTests();
    
    /**
     * @brief 运行系统集成测试
     * @return 测试是否通过
     */
    bool runSystemTests();
    
    /**
     * @brief 获取测试结果统计
     * @return 测试结果统计信息
     */
    struct TestResults {
        int totalTests = 0;
        int passedTests = 0;
        int failedTests = 0;
        std::vector<std::string> failedTestNames;
        
        double getSuccessRate() const {
            return totalTests > 0 ? (double)passedTests / totalTests * 100.0 : 0.0;
        }
    };
    
    TestResults getTestResults() const { return testResults; }
    
private:
    TestResults testResults;
    
    /**
     * @brief 记录测试结果
     * @param testName 测试名称
     * @param passed 是否通过
     */
    void recordTestResult(const std::string& testName, bool passed);
    
    /**
     * @brief 显示测试结果摘要
     */
    void displayTestSummary() const;
    
    /**
     * @brief 重置测试结果
     */
    void resetTestResults();
};

/**
 * @brief 基础功能测试类
 * 
 * 负责测试系统的基础功能，包括：
 * - 工具函数测试
 * - 时间函数测试
 * - 损伤类型转换测试
 * - 单位转换测试
 * - 目录创建测试
 */
class BasicFunctionTest {
public:
    /**
     * @brief 运行所有基础功能测试
     * @return 测试是否全部通过
     */
    static bool runAllTests();
    
    /**
     * @brief 测试时间函数
     * @return 测试是否通过
     */
    static bool testTimeFunctions();
    
    /**
     * @brief 测试损伤类型转换
     * @return 测试是否通过
     */
    static bool testDamageTypeConversion();
    
    /**
     * @brief 测试单位转换
     * @return 测试是否通过
     */
    static bool testUnitConversion();
    
    /**
     * @brief 测试目录创建
     * @return 测试是否通过
     */
    static bool testDirectoryCreation();
    
    /**
     * @brief 测试工具函数
     * @return 测试是否通过
     */
    static bool testUtilityFunctions();
};

#ifdef USE_OPENCV
/**
 * @brief 摄像头功能测试类
 * 
 * 负责测试摄像头相关功能，包括：
 * - 摄像头检测和初始化
 * - 单摄像头采集测试
 * - 多摄像头采集测试
 * - 图像质量验证
 */
class CameraFunctionTest {
public:
    /**
     * @brief 运行所有摄像头功能测试
     * @return 测试是否全部通过
     */
    static bool runAllTests();
    
    /**
     * @brief 测试摄像头检测和初始化
     * @return 测试是否通过
     */
    static bool testCameraDetection();
    
    /**
     * @brief 测试单摄像头采集
     * @param manager 摄像头管理器
     * @return 测试是否通过
     */
    static bool testSingleCameraCapture(CameraManager& manager);
    
    /**
     * @brief 测试多摄像头采集
     * @param manager 摄像头管理器
     * @return 测试是否通过
     */
    static bool testMultiCameraCapture(CameraManager& manager);
    
    /**
     * @brief 测试图像质量
     * @param manager 摄像头管理器
     * @return 测试是否通过
     */
    static bool testImageQuality(CameraManager& manager);

    /**
     * @brief 测试所有摄像头采集功能
     * @param manager 摄像头管理器
     * @return 测试是否通过
     * @note 此测试会验证getAllFrames()方法的实际行为，
     *       并根据配置的摄像头数量给出合理的评估
     */
    static bool testAllCamerasFunction(CameraManager& manager);
    
private:
    /**
     * @brief 验证图像质量
     * @param frame 图像帧
     * @return 图像质量是否合格
     */
    static bool validateImageQuality(const cv::Mat& frame);
};

/**
 * @brief 系统集成测试类
 * 
 * 负责测试系统的整体功能，包括：
 * - 系统启动和关闭测试
 * - 端到端功能测试
 * - 性能压力测试
 * - 错误恢复测试
 */
class SystemIntegrationTest {
public:
    /**
     * @brief 运行所有系统集成测试
     * @return 测试是否全部通过
     */
    static bool runAllTests();
    
    /**
     * @brief 测试系统启动和关闭
     * @return 测试是否通过
     */
    static bool testSystemStartupShutdown();
    
    /**
     * @brief 测试端到端功能
     * @return 测试是否通过
     */
    static bool testEndToEndFunctionality();
    
    /**
     * @brief 测试性能压力
     * @return 测试是否通过
     */
    static bool testPerformanceStress();
    
    /**
     * @brief 测试错误恢复
     * @return 测试是否通过
     */
    static bool testErrorRecovery();
};
#endif // USE_OPENCV
